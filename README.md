# Math Equation Extractor - استخراج المعادلات الرياضية

برنامج Python لاستخراج المعادلات الرياضية من صور الملخصات والكتب وتحويلها إلى صور LaTeX عالية الجودة قابلة للاستخدام في PowerPoint.

## المميزات

- 🔍 **استخراج ذكي للمعادلات**: يستخدم تقنيات OCR متقدمة لاستخراج المعادلات من الصور
- 📐 **تحويل LaTeX**: يحول النصوص المستخرجة إلى كود LaTeX صحيح
- 🖼️ **إنشاء صور عالية الجودة**: ينتج صور PNG بدقة قابلة للتخصيص
- 🎨 **تخصيص المظهر**: إمكانية تغيير الألوان والخطوط والحجم
- 📁 **معالجة مجمعة**: يمكن معالجة عدة صور في نفس الوقت
- 🖥️ **واجهة سهلة الاستخدام**: واجهة رسومية بسيطة وواضحة

## متطلبات التشغيل

### Python 3.8 أو أحدث

### المكتبات المطلوبة:
```bash
pip install -r requirements.txt
```

### متطلبات إضافية:

#### لـ Tesseract OCR:
1. تحميل وتثبيت Tesseract من: https://github.com/UB-Mannheim/tesseract/wiki
2. إضافة مسار Tesseract للمتغير PATH

#### لـ LaTeX (اختياري للجودة العالية):
1. تثبيت MiKTeX أو TeX Live
2. التأكد من وجود `pdflatex` في PATH

## طريقة التشغيل

### 1. تشغيل البرنامج:
```bash
python main.py
```

### 2. استخدام البرنامج:

1. **اختيار الصور**: اضغط على "📁 اختر الصور" واختر صور المعادلات
2. **اختيار طريقة OCR**: من القائمة المنسدلة (EasyOCR موصى به)
3. **تعديل الجودة**: استخدم الشريط المنزلق لتحديد دقة الصورة المُخرجة
4. **بدء المعالجة**: اضغط "🔄 استخراج المعادلات"
5. **مراجعة النتائج**: ستظهر النتائج في الجانب الأيمن
6. **إنشاء الصور**: اضغط "🖼️ إنشاء صورة" لكل معادلة

### 3. النتائج:
- الصور المُنتجة تُحفظ في مجلد `output_equations`
- يمكن نسخ كود LaTeX مباشرة للحافظة
- الصور جاهزة للاستخدام في PowerPoint

## أنواع OCR المدعومة

### 1. EasyOCR (موصى به)
- سهل الاستخدام
- دقة جيدة مع المعادلات البسيطة
- لا يحتاج إعداد إضافي

### 2. Tesseract
- دقة عالية مع النصوص
- يحتاج تثبيت منفصل
- جيد للمعادلات البسيطة

### 3. Pix2Tex
- متخصص في المعادلات الرياضية
- دقة عالية مع المعادلات المعقدة
- قد يحتاج اتصال إنترنت

## أمثلة على الاستخدام

### معادلات مدعومة:
- الكسور: `\frac{a}{b}`
- الأسس: `x^2`, `e^{-x}`
- الجذور: `\sqrt{x}`, `\sqrt[n]{x}`
- التكاملات: `\int f(x) dx`
- المشتقات: `\frac{d}{dx}f(x)`
- الحدود: `\lim_{x \to 0} f(x)`
- المجموعات: `\sum_{i=1}^{n} x_i`

### نصائح للحصول على أفضل النتائج:

1. **جودة الصورة**: استخدم صور واضحة وعالية الدقة
2. **الإضاءة**: تأكد من الإضاءة الجيدة والتباين الواضح
3. **الخلفية**: خلفية بيضاء أو فاتحة تعطي نتائج أفضل
4. **حجم المعادلة**: المعادلات الكبيرة أسهل في الاستخراج
5. **الخط**: الخطوط الواضحة والمطبوعة أفضل من المكتوبة باليد

## استكشاف الأخطاء

### مشاكل شائعة وحلولها:

#### 1. خطأ في تثبيت المكتبات:
```bash
pip install --upgrade pip
pip install -r requirements.txt
```

#### 2. Tesseract غير موجود:
- تأكد من تثبيت Tesseract
- أضف مسار التثبيت للمتغير PATH
- أعد تشغيل البرنامج

#### 3. دقة OCR منخفضة:
- جرب طريقة OCR مختلفة
- حسن جودة الصورة المدخلة
- تأكد من وضوح المعادلة في الصورة

#### 4. فشل في إنشاء الصورة:
- تأكد من وجود مساحة كافية على القرص
- تحقق من صحة كود LaTeX
- جرب تقليل دقة الصورة

## الملفات الرئيسية

- `main.py`: الملف الرئيسي والواجهة الرسومية
- `math_ocr.py`: وحدة استخراج المعادلات بـ OCR
- `latex_to_image.py`: وحدة تحويل LaTeX إلى صور
- `requirements.txt`: قائمة المكتبات المطلوبة

## التطوير المستقبلي

- [ ] دعم المعادلات المكتوبة باليد
- [ ] تحسين دقة استخراج المعادلات المعقدة
- [ ] إضافة المزيد من خيارات التنسيق
- [ ] دعم تصدير لصيغ أخرى (SVG, PDF)
- [ ] إضافة معاينة مباشرة للمعادلات
- [ ] دعم اللغة العربية في المعادلات

## المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء branch جديد للميزة
3. إضافة التحسينات
4. إرسال Pull Request

## الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام الشخصي والتعليمي.

## التواصل

للأسئلة والاقتراحات، يرجى فتح Issue في المشروع.

---

**ملاحظة**: هذا البرنامج في مرحلة التطوير ويمكن أن تحدث تحسينات مستمرة على الأداء والدقة.
