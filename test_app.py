"""
اختبار سريع لوظائف البرنامج
Quick test for application functions
"""

import os
import sys
from pathlib import Path

def test_imports():
    """اختبار استيراد المكتبات"""
    print("Testing imports...")
    
    try:
        import cv2
        print("✓ OpenCV imported successfully")
    except ImportError as e:
        print(f"✗ OpenCV import failed: {e}")
        return False
        
    try:
        import PIL
        print("✓ Pillow imported successfully")
    except ImportError as e:
        print(f"✗ Pillow import failed: {e}")
        return False
        
    try:
        import customtkinter
        print("✓ CustomTkinter imported successfully")
    except ImportError as e:
        print(f"✗ CustomTkinter import failed: {e}")
        return False
        
    try:
        import easyocr
        print("✓ EasyOCR imported successfully")
    except ImportError as e:
        print(f"✗ EasyOCR import failed: {e}")
        return False
        
    try:
        import matplotlib
        print("✓ Matplotlib imported successfully")
    except ImportError as e:
        print(f"✗ Matplotlib import failed: {e}")
        return False
        
    return True

def test_modules():
    """اختبار الوحدات المخصصة"""
    print("\nTesting custom modules...")
    
    try:
        from math_ocr import MathOCR
        print("✓ MathOCR module imported successfully")
        
        # اختبار إنشاء كائن
        ocr = MathOCR(method="easyocr")
        print("✓ MathOCR object created successfully")
        
    except Exception as e:
        print(f"✗ MathOCR test failed: {e}")
        return False
        
    try:
        from latex_to_image import LaTeXToImageConverter
        print("✓ LaTeXToImageConverter module imported successfully")
        
        # اختبار إنشاء كائن
        converter = LaTeXToImageConverter()
        print("✓ LaTeXToImageConverter object created successfully")
        
    except Exception as e:
        print(f"✗ LaTeXToImageConverter test failed: {e}")
        return False
        
    return True

def test_latex_conversion():
    """اختبار تحويل LaTeX"""
    print("\nTesting LaTeX conversion...")
    
    try:
        from latex_to_image import LaTeXToImageConverter
        
        converter = LaTeXToImageConverter(dpi=100)
        
        # معادلة تجريبية بسيطة
        test_equation = r"x^2 + y^2 = r^2"
        
        output_path = converter.convert_latex_to_image(
            test_equation, 
            method="matplotlib"
        )
        
        if output_path and os.path.exists(output_path):
            print(f"✓ LaTeX conversion successful: {output_path}")
            return True
        else:
            print("✗ LaTeX conversion failed: No output file")
            return False
            
    except Exception as e:
        print(f"✗ LaTeX conversion test failed: {e}")
        return False

def test_directories():
    """اختبار إنشاء المجلدات"""
    print("\nTesting directory creation...")
    
    output_dir = Path("output_equations")
    
    try:
        output_dir.mkdir(exist_ok=True)
        
        if output_dir.exists():
            print(f"✓ Output directory created: {output_dir}")
            return True
        else:
            print("✗ Failed to create output directory")
            return False
            
    except Exception as e:
        print(f"✗ Directory creation failed: {e}")
        return False

def create_sample_image():
    """إنشاء صورة تجريبية للاختبار"""
    print("\nCreating sample test image...")
    
    try:
        import matplotlib.pyplot as plt
        import matplotlib.patches as patches
        
        # إنشاء صورة تحتوي على معادلة بسيطة
        fig, ax = plt.subplots(figsize=(6, 2))
        ax.text(0.5, 0.5, r'$E = mc^2$', 
               transform=ax.transAxes,
               fontsize=20,
               ha='center', va='center')
        ax.axis('off')
        
        sample_path = "sample_equation.png"
        plt.savefig(sample_path, dpi=150, bbox_inches='tight')
        plt.close()
        
        if os.path.exists(sample_path):
            print(f"✓ Sample image created: {sample_path}")
            return sample_path
        else:
            print("✗ Failed to create sample image")
            return None
            
    except Exception as e:
        print(f"✗ Sample image creation failed: {e}")
        return None

def main():
    """تشغيل جميع الاختبارات"""
    print("=" * 50)
    print("Math Equation Extractor - Test Suite")
    print("=" * 50)
    
    tests_passed = 0
    total_tests = 5
    
    # اختبار الاستيرادات
    if test_imports():
        tests_passed += 1
        
    # اختبار الوحدات المخصصة
    if test_modules():
        tests_passed += 1
        
    # اختبار المجلدات
    if test_directories():
        tests_passed += 1
        
    # اختبار تحويل LaTeX
    if test_latex_conversion():
        tests_passed += 1
        
    # إنشاء صورة تجريبية
    if create_sample_image():
        tests_passed += 1
    
    print("\n" + "=" * 50)
    print(f"Test Results: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("✓ All tests passed! The application should work correctly.")
        print("\nYou can now run the main application with:")
        print("python main.py")
    else:
        print("✗ Some tests failed. Please check the error messages above.")
        print("\nTry installing missing packages with:")
        print("pip install -r requirements.txt")
    
    print("=" * 50)
    
    return tests_passed == total_tests

if __name__ == "__main__":
    success = main()
    
    # انتظار إدخال المستخدم قبل الإغلاق
    input("\nPress Enter to exit...")
    
    sys.exit(0 if success else 1)
