"""
LaTeX to Image Converter - محول LaTeX إلى صور
يحول كود LaTeX إلى صور عالية الجودة قابلة للاستخدام في PowerPoint
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib import mathtext
import numpy as np
from PIL import Image, ImageDraw, ImageFont
import io
import os
import requests
import base64
from pathlib import Path
import sympy as sp
from sympy import latex, preview
import tempfile
import subprocess

class LaTeXToImageConverter:
    def __init__(self, dpi=150, background_color='white', text_color='black'):
        """
        تهيئة محول LaTeX إلى صور
        
        Args:
            dpi: دقة الصورة (نقطة لكل بوصة)
            background_color: لون الخلفية
            text_color: لون النص
        """
        self.dpi = dpi
        self.background_color = background_color
        self.text_color = text_color
        self.output_dir = Path("output_equations")
        self.output_dir.mkdir(exist_ok=True)
        
        # إعداد matplotlib
        plt.rcParams['mathtext.fontset'] = 'cm'
        plt.rcParams['font.size'] = 14
        
    def clean_latex_code(self, latex_code: str) -> str:
        """
        تنظيف كود LaTeX وإعداده للتحويل
        
        Args:
            latex_code: كود LaTeX
            
        Returns:
            كود LaTeX منظف
        """
        if not latex_code:
            return ""
            
        # إزالة $ إذا كانت موجودة
        cleaned = latex_code.strip()
        if cleaned.startswith('$') and cleaned.endswith('$'):
            cleaned = cleaned[1:-1]
        elif cleaned.startswith('$$') and cleaned.endswith('$$'):
            cleaned = cleaned[2:-2]
            
        # إزالة المسافات الزائدة
        cleaned = cleaned.strip()
        
        # إصلاح بعض الأخطاء الشائعة
        fixes = {
            '\\\\': '\\',  # إزالة الباك سلاش المضاعف
            '{ }': '',     # إزالة الأقواس الفارغة
            '  ': ' ',     # إزالة المسافات المضاعفة
        }
        
        for old, new in fixes.items():
            cleaned = cleaned.replace(old, new)
            
        return cleaned
        
    def create_with_matplotlib(self, latex_code: str, output_path: str = None) -> str:
        """
        إنشاء صورة باستخدام matplotlib
        
        Args:
            latex_code: كود LaTeX
            output_path: مسار حفظ الصورة
            
        Returns:
            مسار الصورة المحفوظة
        """
        if not output_path:
            output_path = self.output_dir / f"equation_{hash(latex_code)}.png"
            
        # تنظيف الكود
        cleaned_latex = self.clean_latex_code(latex_code)
        
        try:
            # إنشاء figure
            fig, ax = plt.subplots(figsize=(10, 3))
            ax.axis('off')
            
            # إضافة النص الرياضي
            ax.text(0.5, 0.5, f'${cleaned_latex}$', 
                   transform=ax.transAxes,
                   fontsize=16,
                   ha='center', va='center',
                   color=self.text_color)
            
            # تعيين لون الخلفية
            fig.patch.set_facecolor(self.background_color)
            
            # حفظ الصورة
            plt.savefig(output_path, 
                       dpi=self.dpi,
                       bbox_inches='tight',
                       facecolor=self.background_color,
                       edgecolor='none',
                       pad_inches=0.1)
            plt.close()
            
            return str(output_path)
            
        except Exception as e:
            print(f"خطأ في إنشاء الصورة بـ matplotlib: {e}")
            return None
            
    def create_with_sympy(self, latex_code: str, output_path: str = None) -> str:
        """
        إنشاء صورة باستخدام SymPy
        
        Args:
            latex_code: كود LaTeX
            output_path: مسار حفظ الصورة
            
        Returns:
            مسار الصورة المحفوظة
        """
        if not output_path:
            output_path = self.output_dir / f"equation_sympy_{hash(latex_code)}.png"
            
        # تنظيف الكود
        cleaned_latex = self.clean_latex_code(latex_code)
        
        try:
            # محاولة تحليل التعبير الرياضي
            expr = sp.sympify(cleaned_latex.replace('\\', ''))
            
            # تحويل لـ LaTeX
            latex_expr = sp.latex(expr)
            
            # إنشاء الصورة
            with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp:
                preview(f'${latex_expr}$', 
                       viewer='file', 
                       filename=tmp.name,
                       dvioptions=['-T', 'tight', '-z', '0', '--truecolor', '-D', str(self.dpi)])
                
                # نسخ الملف للمسار المطلوب
                import shutil
                shutil.copy(tmp.name, output_path)
                os.unlink(tmp.name)
                
            return str(output_path)
            
        except Exception as e:
            print(f"خطأ في إنشاء الصورة بـ SymPy: {e}")
            # العودة لـ matplotlib كبديل
            return self.create_with_matplotlib(latex_code, output_path)
            
    def create_with_codecogs_api(self, latex_code: str, output_path: str = None) -> str:
        """
        إنشاء صورة باستخدام CodeCogs API
        
        Args:
            latex_code: كود LaTeX
            output_path: مسار حفظ الصورة
            
        Returns:
            مسار الصورة المحفوظة
        """
        if not output_path:
            output_path = self.output_dir / f"equation_codecogs_{hash(latex_code)}.png"
            
        # تنظيف الكود
        cleaned_latex = self.clean_latex_code(latex_code)
        
        try:
            # إعداد URL لـ CodeCogs
            base_url = "https://latex.codecogs.com/png.latex"
            
            # إعداد المعاملات
            params = {
                'dpi': self.dpi,
                'bg': self.background_color,
                'fg': self.text_color.replace('#', ''),
            }
            
            # بناء URL
            url = f"{base_url}?{cleaned_latex}"
            
            # إرسال الطلب
            response = requests.get(url, params=params, timeout=10)
            
            if response.status_code == 200:
                # حفظ الصورة
                with open(output_path, 'wb') as f:
                    f.write(response.content)
                return str(output_path)
            else:
                print(f"فشل في الحصول على الصورة من CodeCogs: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"خطأ في استخدام CodeCogs API: {e}")
            # العودة لـ matplotlib كبديل
            return self.create_with_matplotlib(latex_code, output_path)
            
    def create_custom_styled_image(self, latex_code: str, output_path: str = None, 
                                 style_options: dict = None) -> str:
        """
        إنشاء صورة مخصصة مع خيارات تنسيق متقدمة
        
        Args:
            latex_code: كود LaTeX
            output_path: مسار حفظ الصورة
            style_options: خيارات التنسيق
            
        Returns:
            مسار الصورة المحفوظة
        """
        if not output_path:
            output_path = self.output_dir / f"equation_custom_{hash(latex_code)}.png"
            
        if not style_options:
            style_options = {}
            
        # الخيارات الافتراضية
        default_options = {
            'font_size': 16,
            'padding': 20,
            'border': False,
            'border_color': 'black',
            'border_width': 1,
            'shadow': False,
            'shadow_offset': (2, 2),
            'shadow_color': 'gray'
        }
        
        # دمج الخيارات
        options = {**default_options, **style_options}
        
        # تنظيف الكود
        cleaned_latex = self.clean_latex_code(latex_code)
        
        try:
            # إنشاء figure مع حجم مخصص
            fig, ax = plt.subplots(figsize=(12, 4))
            ax.axis('off')
            
            # إضافة الظل إذا كان مطلوباً
            if options['shadow']:
                shadow_x = 0.5 + options['shadow_offset'][0]/100
                shadow_y = 0.5 - options['shadow_offset'][1]/100
                ax.text(shadow_x, shadow_y, f'${cleaned_latex}$',
                       transform=ax.transAxes,
                       fontsize=options['font_size'],
                       ha='center', va='center',
                       color=options['shadow_color'],
                       alpha=0.5)
            
            # إضافة النص الرئيسي
            ax.text(0.5, 0.5, f'${cleaned_latex}$',
                   transform=ax.transAxes,
                   fontsize=options['font_size'],
                   ha='center', va='center',
                   color=self.text_color)
            
            # إضافة إطار إذا كان مطلوباً
            if options['border']:
                rect = patches.Rectangle((0.05, 0.05), 0.9, 0.9,
                                       linewidth=options['border_width'],
                                       edgecolor=options['border_color'],
                                       facecolor='none',
                                       transform=ax.transAxes)
                ax.add_patch(rect)
            
            # تعيين لون الخلفية
            fig.patch.set_facecolor(self.background_color)
            
            # حفظ الصورة
            plt.savefig(output_path,
                       dpi=self.dpi,
                       bbox_inches='tight',
                       facecolor=self.background_color,
                       edgecolor='none',
                       pad_inches=options['padding']/100)
            plt.close()
            
            return str(output_path)
            
        except Exception as e:
            print(f"خطأ في إنشاء الصورة المخصصة: {e}")
            return None
            
    def convert_latex_to_image(self, latex_code: str, method: str = "matplotlib", 
                             output_path: str = None, style_options: dict = None) -> str:
        """
        تحويل كود LaTeX إلى صورة
        
        Args:
            latex_code: كود LaTeX
            method: طريقة التحويل ("matplotlib", "sympy", "codecogs", "custom")
            output_path: مسار حفظ الصورة
            style_options: خيارات التنسيق
            
        Returns:
            مسار الصورة المحفوظة
        """
        if not latex_code or not latex_code.strip():
            return None
            
        if method == "matplotlib":
            return self.create_with_matplotlib(latex_code, output_path)
        elif method == "sympy":
            return self.create_with_sympy(latex_code, output_path)
        elif method == "codecogs":
            return self.create_with_codecogs_api(latex_code, output_path)
        elif method == "custom":
            return self.create_custom_styled_image(latex_code, output_path, style_options)
        else:
            # افتراضي: matplotlib
            return self.create_with_matplotlib(latex_code, output_path)
            
    def batch_convert(self, latex_equations: list, method: str = "matplotlib") -> list:
        """
        تحويل مجموعة من المعادلات دفعة واحدة
        
        Args:
            latex_equations: قائمة بأكواد LaTeX
            method: طريقة التحويل
            
        Returns:
            قائمة بمسارات الصور المحفوظة
        """
        results = []
        
        for i, latex_code in enumerate(latex_equations):
            output_path = self.output_dir / f"batch_equation_{i+1}.png"
            result = self.convert_latex_to_image(latex_code, method, str(output_path))
            
            if result:
                results.append(result)
            else:
                print(f"فشل في تحويل المعادلة {i+1}: {latex_code}")
                
        return results

# مثال للاستخدام
if __name__ == "__main__":
    # اختبار سريع
    converter = LaTeXToImageConverter(dpi=200)
    
    # معادلة تجريبية
    test_equation = r"\frac{d}{dx}[f(x)] = \lim_{h \to 0} \frac{f(x+h) - f(x)}{h}"
    
    # تحويل المعادلة
    result = converter.convert_latex_to_image(test_equation, method="matplotlib")
    
    if result:
        print(f"تم إنشاء الصورة: {result}")
    else:
        print("فشل في إنشاء الصورة")
        
    print("LaTeX to Image converter loaded successfully!")
