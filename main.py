"""
Math Equation Extractor from Images to LaTeX Images
برنامج استخراج المعادلات الرياضية من الصور وتحويلها لصور LaTeX

المطور: <PERSON>
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import customtkinter as ctk
from PIL import Image, ImageTk
import cv2
import numpy as np
import os
import threading
from pathlib import Path

# استيراد الوحدات المخصصة
from math_ocr import MathOCR
from latex_to_image import LaTeXToImageConverter

# Set appearance mode and color theme
ctk.set_appearance_mode("dark")
ctk.set_default_color_theme("blue")

class MathExtractorApp:
    def __init__(self):
        self.root = ctk.CTk()
        self.root.title("Math Equation Extractor - استخراج المعادلات الرياضية")
        self.root.geometry("1200x800")

        # Variables
        self.input_images = []
        self.extracted_equations = []
        self.output_folder = "output_equations"

        # Create output folder
        Path(self.output_folder).mkdir(exist_ok=True)

        # Initialize OCR and converter
        self.math_ocr = MathOCR(method="easyocr")
        self.latex_converter = LaTeXToImageConverter(dpi=150)

        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        
        # Main title
        title_label = ctk.CTkLabel(
            self.root, 
            text="استخراج المعادلات الرياضية من الصور",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=20)
        
        # Create main frame
        main_frame = ctk.CTkFrame(self.root)
        main_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        # Left panel for controls
        left_panel = ctk.CTkFrame(main_frame)
        left_panel.pack(side="left", fill="y", padx=10, pady=10)
        
        # Upload button
        upload_btn = ctk.CTkButton(
            left_panel,
            text="📁 اختر الصور",
            command=self.upload_images,
            font=ctk.CTkFont(size=16),
            height=40
        )
        upload_btn.pack(pady=10, padx=20, fill="x")
        
        # Process button
        self.process_btn = ctk.CTkButton(
            left_panel,
            text="🔄 استخراج المعادلات",
            command=self.process_images,
            font=ctk.CTkFont(size=16),
            height=40,
            state="disabled"
        )
        self.process_btn.pack(pady=10, padx=20, fill="x")
        
        # Progress bar
        self.progress = ctk.CTkProgressBar(left_panel)
        self.progress.pack(pady=10, padx=20, fill="x")
        self.progress.set(0)
        
        # Status label
        self.status_label = ctk.CTkLabel(
            left_panel,
            text="جاهز للبدء",
            font=ctk.CTkFont(size=12)
        )
        self.status_label.pack(pady=5)
        
        # Settings frame
        settings_frame = ctk.CTkFrame(left_panel)
        settings_frame.pack(pady=20, padx=20, fill="x")
        
        settings_title = ctk.CTkLabel(
            settings_frame,
            text="الإعدادات",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        settings_title.pack(pady=10)
        
        # OCR Method selection
        ocr_label = ctk.CTkLabel(settings_frame, text="طريقة OCR:")
        ocr_label.pack(pady=5)
        
        self.ocr_method = ctk.CTkOptionMenu(
            settings_frame,
            values=["EasyOCR", "Tesseract", "Pix2Tex"],
            command=self.on_ocr_method_change
        )
        self.ocr_method.pack(pady=5, padx=10, fill="x")
        
        # Image quality settings
        quality_label = ctk.CTkLabel(settings_frame, text="جودة الصورة المُخرجة:")
        quality_label.pack(pady=5)
        
        self.quality_slider = ctk.CTkSlider(
            settings_frame,
            from_=100,
            to=300,
            number_of_steps=20
        )
        self.quality_slider.set(150)
        self.quality_slider.pack(pady=5, padx=10, fill="x")
        
        # Right panel for results
        right_panel = ctk.CTkFrame(main_frame)
        right_panel.pack(side="right", fill="both", expand=True, padx=10, pady=10)
        
        # Results title
        results_title = ctk.CTkLabel(
            right_panel,
            text="النتائج",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        results_title.pack(pady=10)
        
        # Results scrollable frame
        self.results_frame = ctk.CTkScrollableFrame(right_panel)
        self.results_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
    def upload_images(self):
        """رفع الصور"""
        file_types = [
            ("Image files", "*.png *.jpg *.jpeg *.bmp *.tiff *.gif"),
            ("PNG files", "*.png"),
            ("JPEG files", "*.jpg *.jpeg"),
            ("All files", "*.*")
        ]
        
        files = filedialog.askopenfilenames(
            title="اختر صور المعادلات",
            filetypes=file_types
        )
        
        if files:
            self.input_images = list(files)
            self.status_label.configure(text=f"تم اختيار {len(files)} صورة")
            self.process_btn.configure(state="normal")
            
            # Clear previous results
            for widget in self.results_frame.winfo_children():
                widget.destroy()
                
    def on_ocr_method_change(self, choice):
        """تغيير طريقة OCR"""
        method_map = {
            "EasyOCR": "easyocr",
            "Tesseract": "tesseract",
            "Pix2Tex": "codecogs"
        }

        if choice in method_map:
            self.math_ocr = MathOCR(method=method_map[choice])
            self.status_label.configure(text=f"تم اختيار {choice}")

    def process_images(self):
        """معالجة الصور في thread منفصل"""
        self.process_btn.configure(state="disabled")
        self.progress.set(0)
        
        # Start processing in separate thread
        thread = threading.Thread(target=self._process_images_thread)
        thread.daemon = True
        thread.start()
        
    def _process_images_thread(self):
        """معالجة الصور الفعلية"""
        try:
            total_images = len(self.input_images)
            
            for i, image_path in enumerate(self.input_images):
                # Update progress
                progress_value = (i + 1) / total_images
                self.progress.set(progress_value)
                
                # Update status
                self.root.after(0, lambda: self.status_label.configure(
                    text=f"معالجة الصورة {i+1} من {total_images}"
                ))
                
                # Process single image
                result = self.process_single_image(image_path)
                
                if result:
                    # Add result to UI
                    self.root.after(0, lambda r=result: self.add_result_to_ui(r))
                    
            # Processing complete
            self.root.after(0, lambda: self.status_label.configure(
                text=f"تم الانتهاء! تم معالجة {total_images} صورة"
            ))
            self.root.after(0, lambda: self.process_btn.configure(state="normal"))
            
        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror(
                "خطأ", f"حدث خطأ أثناء المعالجة: {str(e)}"
            ))
            self.root.after(0, lambda: self.process_btn.configure(state="normal"))
            
    def process_single_image(self, image_path):
        """معالجة صورة واحدة"""
        try:
            # استخراج المعادلات من الصورة
            equations = self.math_ocr.extract_equations(image_path)

            if equations:
                # أخذ أول معادلة مستخرجة
                first_eq = equations[0]
                return {
                    'original_path': image_path,
                    'latex_code': first_eq.get('latex', ''),
                    'confidence': first_eq.get('confidence', 0),
                    'output_image_path': None,
                    'all_equations': equations
                }
            else:
                # إذا لم يتم العثور على معادلات، إرجاع معادلة تجريبية
                return {
                    'original_path': image_path,
                    'latex_code': r'\text{لم يتم العثور على معادلات}',
                    'confidence': 0,
                    'output_image_path': None,
                    'all_equations': []
                }

        except Exception as e:
            print(f"خطأ في معالجة الصورة {image_path}: {e}")
            return {
                'original_path': image_path,
                'latex_code': f'\\text{{خطأ: {str(e)}}}',
                'confidence': 0,
                'output_image_path': None,
                'all_equations': []
            }
        
    def add_result_to_ui(self, result):
        """إضافة النتيجة للواجهة"""
        # Create result frame
        result_frame = ctk.CTkFrame(self.results_frame)
        result_frame.pack(fill="x", pady=5, padx=5)
        
        # Original image name
        image_name = os.path.basename(result['original_path'])
        name_label = ctk.CTkLabel(
            result_frame,
            text=f"📄 {image_name}",
            font=ctk.CTkFont(size=12, weight="bold")
        )
        name_label.pack(anchor="w", padx=10, pady=5)
        
        # LaTeX code
        latex_label = ctk.CTkLabel(
            result_frame,
            text=f"LaTeX: {result['latex_code']}",
            font=ctk.CTkFont(size=10),
            wraplength=400
        )
        latex_label.pack(anchor="w", padx=10, pady=2)
        
        # Buttons frame
        buttons_frame = ctk.CTkFrame(result_frame)
        buttons_frame.pack(fill="x", padx=10, pady=5)
        
        # Copy LaTeX button
        copy_btn = ctk.CTkButton(
            buttons_frame,
            text="📋 نسخ LaTeX",
            width=100,
            height=30,
            command=lambda: self.copy_to_clipboard(result['latex_code'])
        )
        copy_btn.pack(side="left", padx=5)
        
        # Generate image button
        generate_btn = ctk.CTkButton(
            buttons_frame,
            text="🖼️ إنشاء صورة",
            width=100,
            height=30,
            command=lambda: self.generate_latex_image(result)
        )
        generate_btn.pack(side="left", padx=5)
        
    def copy_to_clipboard(self, text):
        """نسخ النص للحافظة"""
        self.root.clipboard_clear()
        self.root.clipboard_append(text)
        messagebox.showinfo("تم", "تم نسخ كود LaTeX للحافظة")
        
    def generate_latex_image(self, result):
        """إنشاء صورة من كود LaTeX"""
        try:
            latex_code = result.get('latex_code', '')
            if not latex_code:
                messagebox.showerror("خطأ", "لا يوجد كود LaTeX لتحويله")
                return

            # تحديد جودة الصورة من الشريط المنزلق
            dpi = int(self.quality_slider.get())
            self.latex_converter.dpi = dpi

            # تحويل LaTeX إلى صورة
            self.status_label.configure(text="جاري إنشاء الصورة...")

            output_path = self.latex_converter.convert_latex_to_image(
                latex_code,
                method="matplotlib"
            )

            if output_path and os.path.exists(output_path):
                # تحديث النتيجة بمسار الصورة
                result['output_image_path'] = output_path

                # عرض رسالة نجاح مع خيار فتح المجلد
                response = messagebox.askyesno(
                    "تم بنجاح",
                    f"تم إنشاء الصورة بنجاح!\nالمسار: {output_path}\n\nهل تريد فتح مجلد الصور؟"
                )

                if response:
                    # فتح مجلد الصور
                    import subprocess
                    subprocess.Popen(f'explorer "{os.path.dirname(output_path)}"')

                self.status_label.configure(text="تم إنشاء الصورة بنجاح")

            else:
                messagebox.showerror("خطأ", "فشل في إنشاء الصورة")
                self.status_label.configure(text="فشل في إنشاء الصورة")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء إنشاء الصورة: {str(e)}")
            self.status_label.configure(text="خطأ في إنشاء الصورة")
        
    def run(self):
        """تشغيل التطبيق"""
        self.root.mainloop()

if __name__ == "__main__":
    app = MathExtractorApp()
    app.run()
