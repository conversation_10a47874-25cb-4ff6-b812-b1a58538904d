"""
Math OCR Module - وحدة استخراج المعادلات الرياضية
يحتوي على مختلف طرق OCR لاستخراج المعادلات من الصور
"""

import cv2
import numpy as np
from PIL import Image
import pytesseract
import easyocr
import re
import requests
import base64
import io
import os
from typing import List, Dict, Optional, Tuple

class MathOCR:
    def __init__(self, method="easyocr"):
        """
        تهيئة كلاس OCR للمعادلات الرياضية
        
        Args:
            method: طريقة OCR ("easyocr", "tesseract", "pix2tex", "codecogs")
        """
        self.method = method
        self.easyocr_reader = None
        
        if method == "easyocr":
            self.easyocr_reader = easyocr.Reader(['en'])
            
    def preprocess_image(self, image_path: str) -> np.ndarray:
        """
        معالجة مسبقة للصورة لتحسين دقة OCR
        
        Args:
            image_path: مسار الصورة
            
        Returns:
            الصورة بعد المعالجة
        """
        # قراءة الصورة
        img = cv2.imread(image_path)
        
        if img is None:
            raise ValueError(f"لا يمكن قراءة الصورة: {image_path}")
            
        # تحويل لرمادي
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        
        # تحسين التباين
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
        enhanced = clahe.apply(gray)
        
        # إزالة الضوضاء
        denoised = cv2.medianBlur(enhanced, 3)
        
        # تحسين الحدود
        kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
        sharpened = cv2.filter2D(denoised, -1, kernel)
        
        # تطبيق threshold
        _, binary = cv2.threshold(sharpened, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        
        return binary
        
    def extract_math_regions(self, image: np.ndarray) -> List[Tuple[int, int, int, int]]:
        """
        استخراج المناطق التي تحتوي على معادلات رياضية
        
        Args:
            image: الصورة المعالجة
            
        Returns:
            قائمة بإحداثيات المناطق (x, y, w, h)
        """
        # البحث عن الكونتورات
        contours, _ = cv2.findContours(image, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        math_regions = []
        
        for contour in contours:
            x, y, w, h = cv2.boundingRect(contour)
            
            # فلترة المناطق بناءً على الحجم
            if w > 50 and h > 20 and w < image.shape[1] * 0.8:
                # حساب نسبة العرض للارتفاع
                aspect_ratio = w / h
                
                # المعادلات عادة ما تكون أوسع من النص العادي
                if 1.5 <= aspect_ratio <= 10:
                    math_regions.append((x, y, w, h))
                    
        return math_regions
        
    def extract_with_easyocr(self, image_path: str) -> List[Dict]:
        """
        استخراج النص باستخدام EasyOCR
        
        Args:
            image_path: مسار الصورة
            
        Returns:
            قائمة بالنصوص المستخرجة
        """
        if not self.easyocr_reader:
            self.easyocr_reader = easyocr.Reader(['en'])
            
        # معالجة الصورة
        processed_img = self.preprocess_image(image_path)
        
        # استخراج النص
        results = self.easyocr_reader.readtext(processed_img)
        
        extracted_texts = []
        for (bbox, text, confidence) in results:
            if confidence > 0.5:  # فلترة النتائج ضعيفة الثقة
                # تنظيف النص وتحويله لـ LaTeX
                latex_text = self.convert_to_latex(text)
                if latex_text:
                    extracted_texts.append({
                        'text': text,
                        'latex': latex_text,
                        'confidence': confidence,
                        'bbox': bbox
                    })
                    
        return extracted_texts
        
    def extract_with_tesseract(self, image_path: str) -> List[Dict]:
        """
        استخراج النص باستخدام Tesseract
        
        Args:
            image_path: مسار الصورة
            
        Returns:
            قائمة بالنصوص المستخرجة
        """
        # معالجة الصورة
        processed_img = self.preprocess_image(image_path)
        
        # إعدادات Tesseract للمعادلات الرياضية
        custom_config = r'--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789+-*/=()[]{}^_abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ'
        
        # استخراج النص
        text = pytesseract.image_to_string(processed_img, config=custom_config)
        
        if text.strip():
            latex_text = self.convert_to_latex(text.strip())
            return [{
                'text': text.strip(),
                'latex': latex_text,
                'confidence': 0.8,  # تقدير افتراضي
                'bbox': None
            }]
            
        return []
        
    def extract_with_codecogs_api(self, image_path: str) -> List[Dict]:
        """
        استخراج المعادلات باستخدام API مشابه لـ codecogs
        
        Args:
            image_path: مسار الصورة
            
        Returns:
            قائمة بالمعادلات المستخرجة
        """
        try:
            # تحويل الصورة لـ base64
            with open(image_path, "rb") as img_file:
                img_base64 = base64.b64encode(img_file.read()).decode()
                
            # هنا يمكن استخدام API حقيقي مثل Mathpix
            # لكن للآن سنستخدم معالجة محلية
            
            # معالجة محلية بسيطة
            processed_img = self.preprocess_image(image_path)
            
            # استخراج المناطق الرياضية
            math_regions = self.extract_math_regions(processed_img)
            
            results = []
            for i, (x, y, w, h) in enumerate(math_regions):
                # قطع المنطقة
                region = processed_img[y:y+h, x:x+w]
                
                # محاولة استخراج النص
                text = pytesseract.image_to_string(region)
                
                if text.strip():
                    latex_text = self.convert_to_latex(text.strip())
                    results.append({
                        'text': text.strip(),
                        'latex': latex_text,
                        'confidence': 0.7,
                        'bbox': (x, y, w, h),
                        'region_id': i
                    })
                    
            return results
            
        except Exception as e:
            print(f"خطأ في استخراج المعادلات: {e}")
            return []
            
    def convert_to_latex(self, text: str) -> str:
        """
        تحويل النص المستخرج إلى كود LaTeX
        
        Args:
            text: النص المستخرج
            
        Returns:
            كود LaTeX
        """
        if not text or not text.strip():
            return ""
            
        latex_text = text.strip()
        
        # قواعد تحويل أساسية
        conversions = {
            # الكسور
            r'(\w+)/(\w+)': r'\\frac{\1}{\2}',
            r'(\d+)/(\d+)': r'\\frac{\1}{\2}',
            
            # الأسس
            r'(\w+)\^(\w+)': r'\1^{\2}',
            r'(\w+)\^(\d+)': r'\1^{\2}',
            
            # الجذور
            r'sqrt\(([^)]+)\)': r'\\sqrt{\1}',
            r'√([^\\s]+)': r'\\sqrt{\1}',
            
            # الرموز الرياضية
            r'alpha': r'\\alpha',
            r'beta': r'\\beta',
            r'gamma': r'\\gamma',
            r'delta': r'\\delta',
            r'epsilon': r'\\epsilon',
            r'theta': r'\\theta',
            r'lambda': r'\\lambda',
            r'mu': r'\\mu',
            r'pi': r'\\pi',
            r'sigma': r'\\sigma',
            r'phi': r'\\phi',
            r'omega': r'\\omega',
            
            # العمليات
            r'\\*': r'\\cdot',
            r'<=': r'\\leq',
            r'>=': r'\\geq',
            r'!=': r'\\neq',
            r'infinity': r'\\infty',
            r'∞': r'\\infty',
            
            # التكاملات والمشتقات
            r'integral': r'\\int',
            r'∫': r'\\int',
            r'd/dx': r'\\frac{d}{dx}',
            r'partial': r'\\partial',
            r'∂': r'\\partial',
            
            # المجموعات
            r'sum': r'\\sum',
            r'∑': r'\\sum',
            r'product': r'\\prod',
            r'∏': r'\\prod',
            
            # الحدود
            r'limit': r'\\lim',
            r'lim': r'\\lim',
            r'->': r'\\to',
            r'→': r'\\to',
        }
        
        # تطبيق التحويلات
        for pattern, replacement in conversions.items():
            latex_text = re.sub(pattern, replacement, latex_text, flags=re.IGNORECASE)
            
        # إضافة $ للمعادلات إذا لم تكن موجودة
        if not latex_text.startswith('$') and not latex_text.startswith('\\['):
            latex_text = f'${latex_text}$'
            
        return latex_text
        
    def extract_equations(self, image_path: str) -> List[Dict]:
        """
        استخراج المعادلات من الصورة باستخدام الطريقة المحددة
        
        Args:
            image_path: مسار الصورة
            
        Returns:
            قائمة بالمعادلات المستخرجة
        """
        if not os.path.exists(image_path):
            raise FileNotFoundError(f"الصورة غير موجودة: {image_path}")
            
        if self.method == "easyocr":
            return self.extract_with_easyocr(image_path)
        elif self.method == "tesseract":
            return self.extract_with_tesseract(image_path)
        elif self.method == "codecogs":
            return self.extract_with_codecogs_api(image_path)
        else:
            raise ValueError(f"طريقة OCR غير مدعومة: {self.method}")

# مثال للاستخدام
if __name__ == "__main__":
    # اختبار سريع
    ocr = MathOCR(method="easyocr")
    
    # يمكن اختبار الكود هنا مع صورة تجريبية
    print("Math OCR module loaded successfully!")
