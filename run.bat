@echo off
echo ========================================
echo    Math Equation Extractor
echo    استخراج المعادلات الرياضية
echo ========================================
echo.

echo Checking Python installation...
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.8 or later
    pause
    exit /b 1
)

echo Python found!
echo.

echo Installing required packages...
pip install -r requirements.txt

if errorlevel 1 (
    echo.
    echo WARNING: Some packages might not have installed correctly
    echo You can try installing them manually:
    echo pip install opencv-python pillow pytesseract easyocr customtkinter matplotlib
    echo.
)

echo.
echo Starting Math Equation Extractor...
echo.

python main.py

if errorlevel 1 (
    echo.
    echo ERROR: Failed to start the application
    echo Please check the error messages above
    pause
)

echo.
echo Application closed.
pause
